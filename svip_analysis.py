#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交用户SVIP标签组合分析工具
分析高客单价用户的标签组合，形成最优SVIP标准
"""

import pandas as pd
import numpy as np
from itertools import combinations
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SVIPAnalyzer:
    def __init__(self, excel_file):
        """初始化分析器"""
        self.df = pd.read_excel(excel_file)
        self.high_value_threshold = 10000  # 高客单价阈值
        
        # 城市分类
        self.tier1_cities = ['上海', '北京', '深圳', '广州']
        self.new_tier1_cities = ['成都', '杭州', '重庆', '苏州', '武汉', '西安', '南京', 
                                '长沙', '天津', '郑州', '东莞', '无锡', '宁波', '青岛', '合肥']
        self.tier2_cities = ['佛山', '沈阳', '昆明', '济南', '厦门', '福州', '温州', '常州', 
                            '大连', '石家庄', '南宁', '哈尔滨', '金华', '南昌', '长春', '南通', 
                            '泉州', '贵阳', '嘉兴', '太原', '惠州', '徐州', '绍兴', '中山', 
                            '台州', '烟台', '珠海', '保定', '潍坊', '兰州']
        
        self.prepare_data()
    
    def prepare_data(self):
        """数据预处理"""
        print("数据预处理中...")
        print(f"原始数据行数: {len(self.df)}")
        print(f"数据列名: {list(self.df.columns)}")
        
        # 显示前几行数据以了解结构
        print("\n前5行数据:")
        print(self.df.head())
        
        # 检查高客单价用户数量
        if '转化金额' in self.df.columns:
            high_value_users = self.df[self.df['转化金额'] > self.high_value_threshold]
            print(f"\n高客单价用户数量 (>{self.high_value_threshold}): {len(high_value_users)}")
            print(f"高客单价用户占比: {len(high_value_users)/len(self.df)*100:.2f}%")
        
    def analyze_current_svip_standard(self):
        """分析当前SVIP标准的效果"""
        print("\n=== 分析当前SVIP标准效果 ===")
        
        # 需要根据实际数据列名调整
        conditions = []
        
        # 条件1：心力评分4-8分
        if '心力评分' in self.df.columns:
            cond1 = (self.df['心力评分'] >= 4) & (self.df['心力评分'] <= 8)
            conditions.append(('心力评分4-8分', cond1))
        
        # 条件2：工作状态【在职】
        if '工作状态' in self.df.columns:
            cond2 = self.df['工作状态'] == '在职'
            conditions.append(('工作状态在职', cond2))
        
        # 条件3：工作年限3-20年
        if '工作年限' in self.df.columns:
            cond3 = (self.df['工作年限'] >= 3) & (self.df['工作年限'] <= 20)
            conditions.append(('工作年限3-20年', cond3))
        
        # 分析每个条件的效果
        for name, condition in conditions:
            if len(condition) > 0:
                filtered_df = self.df[condition]
                high_value_count = len(filtered_df[filtered_df['转化金额'] > self.high_value_threshold])
                conversion_rate = high_value_count / len(filtered_df) * 100 if len(filtered_df) > 0 else 0
                print(f"{name}: 用户数={len(filtered_df)}, 高客单数={high_value_count}, 转化率={conversion_rate:.2f}%")
    
    def find_optimal_combinations(self):
        """寻找最优标签组合"""
        print("\n=== 寻找最优标签组合 ===")
        
        # 定义可能的标签维度和取值
        tag_dimensions = {}
        
        # 根据实际数据列添加维度
        for col in self.df.columns:
            if col in ['心力评分', '工作年限', '薪资', '自我投入成本']:
                # 数值型字段，创建分段
                if col == '心力评分':
                    tag_dimensions[col] = self.create_score_segments(col)
                elif col == '工作年限':
                    tag_dimensions[col] = self.create_experience_segments(col)
                elif col == '薪资':
                    tag_dimensions[col] = self.create_salary_segments(col)
                elif col == '自我投入成本':
                    tag_dimensions[col] = self.create_investment_segments(col)
            elif col in ['工作状态', '城市', '行业']:
                # 分类型字段
                unique_values = self.df[col].dropna().unique()
                tag_dimensions[col] = [(f"{col}_{val}", self.df[col] == val) for val in unique_values]
        
        return self.evaluate_combinations(tag_dimensions)
    
    def create_score_segments(self, col):
        """创建评分分段"""
        segments = []
        ranges = [(1, 3), (4, 6), (7, 8), (9, 10)]
        for low, high in ranges:
            name = f"{col}_{low}-{high}分"
            condition = (self.df[col] >= low) & (self.df[col] <= high)
            segments.append((name, condition))
        return segments
    
    def create_experience_segments(self, col):
        """创建工作年限分段"""
        segments = []
        ranges = [(0, 2), (3, 5), (6, 10), (11, 20), (21, 100)]
        for low, high in ranges:
            name = f"{col}_{low}-{high}年"
            condition = (self.df[col] >= low) & (self.df[col] <= high)
            segments.append((name, condition))
        return segments
    
    def create_salary_segments(self, col):
        """创建薪资分段"""
        segments = []
        ranges = [(0, 5000), (5001, 8000), (8001, 10000), (10001, 15000), (15001, 999999)]
        for low, high in ranges:
            name = f"{col}_{low}-{high}"
            condition = (self.df[col] >= low) & (self.df[col] <= high)
            segments.append((name, condition))
        return segments
    
    def create_investment_segments(self, col):
        """创建投入成本分段"""
        segments = []
        ranges = [(0, 500), (501, 1000), (1001, 2000), (2001, 5000), (5001, 999999)]
        for low, high in ranges:
            name = f"{col}_{low}-{high}"
            condition = (self.df[col] >= low) & (self.df[col] <= high)
            segments.append((name, condition))
        return segments
    
    def evaluate_combinations(self, tag_dimensions, max_combinations=3):
        """评估标签组合效果"""
        results = []
        
        # 获取所有可能的标签
        all_tags = []
        for dimension, tags in tag_dimensions.items():
            all_tags.extend(tags)
        
        print(f"总共有 {len(all_tags)} 个标签维度")
        
        # 评估不同长度的组合
        for combo_length in range(1, min(max_combinations + 1, len(all_tags) + 1)):
            print(f"\n分析 {combo_length} 个标签的组合...")
            
            combo_count = 0
            for combo in combinations(all_tags, combo_length):
                combo_count += 1
                if combo_count > 1000:  # 限制组合数量避免计算过久
                    break
                
                # 计算组合条件
                combined_condition = pd.Series([True] * len(self.df))
                combo_names = []
                
                for tag_name, tag_condition in combo:
                    combined_condition = combined_condition & tag_condition
                    combo_names.append(tag_name)
                
                # 计算效果指标
                filtered_users = self.df[combined_condition]
                if len(filtered_users) < 10:  # 样本太小跳过
                    continue
                
                high_value_users = filtered_users[filtered_users['转化金额'] > self.high_value_threshold]
                
                metrics = {
                    'combination': ' + '.join(combo_names),
                    'total_users': len(filtered_users),
                    'high_value_users': len(high_value_users),
                    'conversion_rate': len(high_value_users) / len(filtered_users) * 100,
                    'total_revenue': high_value_users['转化金额'].sum(),
                    'avg_revenue': high_value_users['转化金额'].mean() if len(high_value_users) > 0 else 0
                }
                
                results.append(metrics)
        
        return sorted(results, key=lambda x: x['high_value_users'], reverse=True)
    
    def generate_report(self, top_combinations=10):
        """生成分析报告"""
        print("\n" + "="*50)
        print("成交用户SVIP标签组合分析报告")
        print("="*50)
        
        # 基础统计
        total_users = len(self.df)
        high_value_users = len(self.df[self.df['转化金额'] > self.high_value_threshold])
        
        print(f"\n【基础数据统计】")
        print(f"总用户数: {total_users}")
        print(f"高客单用户数: {high_value_users}")
        print(f"整体高客单转化率: {high_value_users/total_users*100:.2f}%")
        
        # 分析当前SVIP标准
        self.analyze_current_svip_standard()
        
        # 寻找最优组合
        optimal_combinations = self.find_optimal_combinations()
        
        print(f"\n【推荐SVIP标签组合 TOP {top_combinations}】")
        for i, combo in enumerate(optimal_combinations[:top_combinations], 1):
            print(f"\n{i}. {combo['combination']}")
            print(f"   覆盖用户数: {combo['total_users']}")
            print(f"   高客单用户数: {combo['high_value_users']}")
            print(f"   转化率: {combo['conversion_rate']:.2f}%")
            print(f"   总收入: {combo['total_revenue']:,.0f}")
            print(f"   平均客单价: {combo['avg_revenue']:,.0f}")
        
        return optimal_combinations

def main():
    """主函数"""
    try:
        # 初始化分析器
        analyzer = SVIPAnalyzer('已成交用户.xlsx')
        
        # 生成分析报告
        results = analyzer.generate_report()
        
        # 保存结果到文件
        if results:
            results_df = pd.DataFrame(results)
            results_df.to_excel('SVIP标签组合分析结果.xlsx', index=False)
            print(f"\n分析结果已保存到: SVIP标签组合分析结果.xlsx")
        
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        print("请检查Excel文件是否存在且格式正确")

if __name__ == "__main__":
    main()
