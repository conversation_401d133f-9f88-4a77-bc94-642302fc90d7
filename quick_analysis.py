#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速SVIP分析 - 生成汇报数据
专门用于领导汇报的核心数据提取
"""

import pandas as pd
import numpy as np
from collections import defaultdict

class QuickSVIPAnalysis:
    def __init__(self, excel_file):
        """初始化快速分析器"""
        print("🚀 启动SVIP标签组合快速分析...")
        self.df = pd.read_excel(excel_file)
        self.high_value_threshold = 10000
        
        print(f"📊 数据加载完成：{len(self.df)} 条用户记录")
        print(f"📋 数据字段：{list(self.df.columns)}")
        
    def get_basic_stats(self):
        """获取基础统计数据"""
        print("\n📈 基础数据统计")
        print("-" * 30)
        
        total_users = len(self.df)
        high_value_users = self.df[self.df['转化金额'] > self.high_value_threshold]
        high_value_count = len(high_value_users)
        
        stats = {
            '总用户数': total_users,
            '高客单用户数': high_value_count,
            '整体转化率': round((high_value_count / total_users) * 100, 2),
            '高客单总收入': int(high_value_users['转化金额'].sum()),
            '平均客单价': int(high_value_users['转化金额'].mean()) if high_value_count > 0 else 0,
            '高客单占比': round((high_value_count / total_users) * 100, 2)
        }
        
        print(f"✅ 总用户数：{stats['总用户数']:,} 人")
        print(f"✅ 高客单用户数：{stats['高客单用户数']:,} 人")
        print(f"✅ 整体转化率：{stats['整体转化率']}%")
        print(f"✅ 高客单总收入：{stats['高客单总收入']:,} 元")
        print(f"✅ 平均客单价：{stats['平均客单价']:,} 元")
        
        return stats
    
    def analyze_current_svip(self):
        """分析当前SVIP标准效果"""
        print("\n🎯 当前SVIP标准效果分析")
        print("-" * 30)
        
        results = {}
        
        # 检查各个条件（根据实际数据字段调整）
        conditions = []
        condition_names = []
        
        # 心力评分4-8分
        if '心力评分' in self.df.columns:
            cond = (self.df['心力评分'] >= 4) & (self.df['心力评分'] <= 8)
            conditions.append(cond)
            condition_names.append('心力评分4-8分')
            
            filtered_df = self.df[cond]
            high_value = filtered_df[filtered_df['转化金额'] > self.high_value_threshold]
            
            result = {
                '覆盖用户数': len(filtered_df),
                '高客单用户数': len(high_value),
                '转化率': round((len(high_value) / len(filtered_df) * 100), 2) if len(filtered_df) > 0 else 0,
                '总收入': int(high_value['转化金额'].sum()),
                '平均客单价': int(high_value['转化金额'].mean()) if len(high_value) > 0 else 0
            }
            results['心力评分4-8分'] = result
            print(f"📊 心力评分4-8分：{result['覆盖用户数']}人 → {result['高客单用户数']}人 ({result['转化率']}%)")
        
        # 工作状态在职
        if '工作状态' in self.df.columns:
            cond = self.df['工作状态'] == '在职'
            conditions.append(cond)
            condition_names.append('工作状态在职')
            
            filtered_df = self.df[cond]
            high_value = filtered_df[filtered_df['转化金额'] > self.high_value_threshold]
            
            result = {
                '覆盖用户数': len(filtered_df),
                '高客单用户数': len(high_value),
                '转化率': round((len(high_value) / len(filtered_df) * 100), 2) if len(filtered_df) > 0 else 0,
                '总收入': int(high_value['转化金额'].sum()),
                '平均客单价': int(high_value['转化金额'].mean()) if len(high_value) > 0 else 0
            }
            results['工作状态在职'] = result
            print(f"📊 工作状态在职：{result['覆盖用户数']}人 → {result['高客单用户数']}人 ({result['转化率']}%)")
        
        # 工作年限3-20年
        if '工作年限' in self.df.columns:
            cond = (self.df['工作年限'] >= 3) & (self.df['工作年限'] <= 20)
            conditions.append(cond)
            condition_names.append('工作年限3-20年')
            
            filtered_df = self.df[cond]
            high_value = filtered_df[filtered_df['转化金额'] > self.high_value_threshold]
            
            result = {
                '覆盖用户数': len(filtered_df),
                '高客单用户数': len(high_value),
                '转化率': round((len(high_value) / len(filtered_df) * 100), 2) if len(filtered_df) > 0 else 0,
                '总收入': int(high_value['转化金额'].sum()),
                '平均客单价': int(high_value['转化金额'].mean()) if len(high_value) > 0 else 0
            }
            results['工作年限3-20年'] = result
            print(f"📊 工作年限3-20年：{result['覆盖用户数']}人 → {result['高客单用户数']}人 ({result['转化率']}%)")
        
        # 组合效果
        if len(conditions) >= 2:
            combined_condition = conditions[0]
            for cond in conditions[1:]:
                combined_condition = combined_condition & cond
            
            filtered_df = self.df[combined_condition]
            high_value = filtered_df[filtered_df['转化金额'] > self.high_value_threshold]
            
            combined_result = {
                '覆盖用户数': len(filtered_df),
                '高客单用户数': len(high_value),
                '转化率': round((len(high_value) / len(filtered_df) * 100), 2) if len(filtered_df) > 0 else 0,
                '总收入': int(high_value['转化金额'].sum()),
                '平均客单价': int(high_value['转化金额'].mean()) if len(high_value) > 0 else 0
            }
            results['当前SVIP组合'] = combined_result
            print(f"🎯 当前SVIP组合：{combined_result['覆盖用户数']}人 → {combined_result['高客单用户数']}人 ({combined_result['转化率']}%)")
        
        return results
    
    def find_best_segments(self):
        """找出各维度最佳分段"""
        print("\n🔍 寻找各维度最佳分段")
        print("-" * 30)
        
        best_segments = {}
        
        # 分析数值型字段
        numeric_columns = self.df.select_dtypes(include=[np.number]).columns
        numeric_columns = [col for col in numeric_columns if col != '转化金额']
        
        for column in numeric_columns:
            if column in ['心力评分', '工作年限', '薪资', '自我投入成本']:
                segments = self.create_segments_for_column(column)
                best_segment = self.find_best_segment(column, segments)
                if best_segment:
                    best_segments[column] = best_segment
                    print(f"📈 {column} 最佳分段：{best_segment['分段名称']} (转化率{best_segment['转化率']}%)")
        
        # 分析分类型字段
        categorical_columns = self.df.select_dtypes(include=['object']).columns
        categorical_columns = [col for col in categorical_columns if col not in ['转化金额']]
        
        for column in categorical_columns:
            if column in ['工作状态', '城市', '行业']:
                best_category = self.find_best_category(column)
                if best_category:
                    best_segments[column] = best_category
                    print(f"📈 {column} 最佳类别：{best_category['分段名称']} (转化率{best_category['转化率']}%)")
        
        return best_segments
    
    def create_segments_for_column(self, column):
        """为指定列创建分段"""
        if '评分' in column:
            return [
                ('1-3分', (self.df[column] >= 1) & (self.df[column] <= 3)),
                ('4-6分', (self.df[column] >= 4) & (self.df[column] <= 6)),
                ('7-8分', (self.df[column] >= 7) & (self.df[column] <= 8)),
                ('9-10分', (self.df[column] >= 9) & (self.df[column] <= 10))
            ]
        elif '年限' in column:
            return [
                ('0-2年', (self.df[column] >= 0) & (self.df[column] <= 2)),
                ('3-5年', (self.df[column] >= 3) & (self.df[column] <= 5)),
                ('6-10年', (self.df[column] >= 6) & (self.df[column] <= 10)),
                ('11-20年', (self.df[column] >= 11) & (self.df[column] <= 20)),
                ('20年以上', self.df[column] > 20)
            ]
        elif '薪资' in column:
            return [
                ('5K以下', self.df[column] < 5000),
                ('5K-8K', (self.df[column] >= 5000) & (self.df[column] < 8000)),
                ('8K-10K', (self.df[column] >= 8000) & (self.df[column] < 10000)),
                ('10K-15K', (self.df[column] >= 10000) & (self.df[column] < 15000)),
                ('15K以上', self.df[column] >= 15000)
            ]
        elif '成本' in column or '投入' in column:
            return [
                ('500以下', self.df[column] < 500),
                ('500-1000', (self.df[column] >= 500) & (self.df[column] < 1000)),
                ('1000-2000', (self.df[column] >= 1000) & (self.df[column] < 2000)),
                ('2000-5000', (self.df[column] >= 2000) & (self.df[column] < 5000)),
                ('5000以上', self.df[column] >= 5000)
            ]
        else:
            # 通用四分位数分段
            q25, q50, q75 = self.df[column].quantile([0.25, 0.5, 0.75])
            return [
                ('低分位', self.df[column] <= q25),
                ('中低分位', (self.df[column] > q25) & (self.df[column] <= q50)),
                ('中高分位', (self.df[column] > q50) & (self.df[column] <= q75)),
                ('高分位', self.df[column] > q75)
            ]
    
    def find_best_segment(self, column, segments):
        """找出最佳分段"""
        best_segment = None
        best_score = 0
        
        for segment_name, condition in segments:
            filtered_df = self.df[condition]
            if len(filtered_df) < 20:  # 样本太小跳过
                continue
            
            high_value = filtered_df[filtered_df['转化金额'] > self.high_value_threshold]
            conversion_rate = (len(high_value) / len(filtered_df) * 100) if len(filtered_df) > 0 else 0
            
            # 综合评分：高客单用户数 * 转化率
            score = len(high_value) * conversion_rate
            
            if score > best_score:
                best_score = score
                best_segment = {
                    '分段名称': f"{column}_{segment_name}",
                    '覆盖用户数': len(filtered_df),
                    '高客单用户数': len(high_value),
                    '转化率': round(conversion_rate, 2),
                    '总收入': int(high_value['转化金额'].sum()),
                    '综合评分': round(score, 2)
                }
        
        return best_segment
    
    def find_best_category(self, column):
        """找出最佳类别"""
        best_category = None
        best_score = 0
        
        for category in self.df[column].dropna().unique():
            filtered_df = self.df[self.df[column] == category]
            if len(filtered_df) < 20:
                continue
            
            high_value = filtered_df[filtered_df['转化金额'] > self.high_value_threshold]
            conversion_rate = (len(high_value) / len(filtered_df) * 100) if len(filtered_df) > 0 else 0
            
            score = len(high_value) * conversion_rate
            
            if score > best_score:
                best_score = score
                best_category = {
                    '分段名称': f"{column}_{category}",
                    '覆盖用户数': len(filtered_df),
                    '高客单用户数': len(high_value),
                    '转化率': round(conversion_rate, 2),
                    '总收入': int(high_value['转化金额'].sum()),
                    '综合评分': round(score, 2)
                }
        
        return best_category
    
    def generate_recommendations(self, best_segments):
        """生成推荐方案"""
        print("\n💡 生成推荐SVIP标准")
        print("-" * 30)
        
        # 选择TOP 3-4个最佳维度
        sorted_segments = sorted(best_segments.items(), 
                               key=lambda x: x[1]['综合评分'], 
                               reverse=True)
        
        top_segments = sorted_segments[:4]  # 选择前4个
        
        print("🏆 推荐新SVIP标准组合：")
        for i, (dimension, segment) in enumerate(top_segments, 1):
            print(f"   条件{i}：{segment['分段名称']}")
            print(f"           转化率{segment['转化率']}%，高客单用户{segment['高客单用户数']}人")
        
        # 估算组合效果（简化计算）
        if len(top_segments) >= 2:
            # 取前两个最强维度的交集作为估算
            dim1, seg1 = top_segments[0]
            dim2, seg2 = top_segments[1]
            
            estimated_users = min(seg1['覆盖用户数'], seg2['覆盖用户数']) * 0.6  # 保守估计交集
            estimated_conversion = (seg1['转化率'] + seg2['转化率']) / 2 * 1.1  # 组合提升
            estimated_high_value = estimated_users * estimated_conversion / 100
            
            recommendation = {
                '推荐标签组合': [seg['分段名称'] for _, seg in top_segments],
                '预估覆盖用户数': int(estimated_users),
                '预估高客单用户数': int(estimated_high_value),
                '预估转化率': round(estimated_conversion, 2),
                '预估收入提升': f"+{int(estimated_high_value * 15000):,}元"  # 假设平均客单价15000
            }
            
            print(f"\n🎯 预期效果：")
            print(f"   预估覆盖用户：{recommendation['预估覆盖用户数']} 人")
            print(f"   预估高客单用户：{recommendation['预估高客单用户数']} 人")
            print(f"   预估转化率：{recommendation['预估转化率']}%")
            print(f"   预估收入提升：{recommendation['预估收入提升']}")
            
            return recommendation
        
        return None
    
    def run_quick_analysis(self):
        """运行快速分析"""
        print("🚀 开始快速SVIP分析...")
        
        # 1. 基础统计
        basic_stats = self.get_basic_stats()
        
        # 2. 当前SVIP效果
        current_svip = self.analyze_current_svip()
        
        # 3. 最佳分段
        best_segments = self.find_best_segments()
        
        # 4. 推荐方案
        recommendation = self.generate_recommendations(best_segments)
        
        # 5. 汇总结果
        results = {
            '基础统计': basic_stats,
            '当前SVIP效果': current_svip,
            '最佳分段': best_segments,
            '推荐方案': recommendation
        }
        
        # 6. 保存结果
        self.save_quick_results(results)
        
        print("\n✅ 快速分析完成！")
        return results
    
    def save_quick_results(self, results):
        """保存快速分析结果"""
        try:
            with pd.ExcelWriter('SVIP快速分析结果.xlsx', engine='openpyxl') as writer:
                # 基础统计
                basic_df = pd.DataFrame([results['基础统计']])
                basic_df.to_excel(writer, sheet_name='基础统计', index=False)
                
                # 当前SVIP效果
                if results['当前SVIP效果']:
                    current_df = pd.DataFrame(results['当前SVIP效果']).T
                    current_df.to_excel(writer, sheet_name='当前SVIP效果')
                
                # 最佳分段
                if results['最佳分段']:
                    segments_df = pd.DataFrame(results['最佳分段']).T
                    segments_df.to_excel(writer, sheet_name='最佳分段')
            
            print(f"📁 结果已保存到：SVIP快速分析结果.xlsx")
            
        except Exception as e:
            print(f"⚠️  保存结果时出现错误：{str(e)}")

def main():
    """主函数"""
    try:
        analyzer = QuickSVIPAnalysis('已成交用户.xlsx')
        results = analyzer.run_quick_analysis()
        
        print("\n🎉 分析完成！请查看生成的Excel文件获取详细数据。")
        print("📋 建议使用这些数据填充PPT汇报模板。")
        
    except FileNotFoundError:
        print("❌ 找不到文件'已成交用户.xlsx'，请确认文件存在。")
    except Exception as e:
        print(f"❌ 分析过程中出现错误：{str(e)}")

if __name__ == "__main__":
    main()
